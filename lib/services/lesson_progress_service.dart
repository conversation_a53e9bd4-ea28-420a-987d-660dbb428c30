import 'package:shared_preferences/shared_preferences.dart';

class LessonProgressService {
  static const String _progressKey = 'lesson_progress';
  
  // Singleton pattern
  static final LessonProgressService _instance = LessonProgressService._internal();
  factory LessonProgressService() => _instance;
  LessonProgressService._internal();

  // In-memory cache for quick access
  Map<String, Set<int>> _courseProgress = {};

  // Initialize the service
  Future<void> initialize() async {
    await _loadProgress();
  }

  // Load progress from SharedPreferences
  Future<void> _loadProgress() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final progressData = prefs.getStringList(_progressKey) ?? [];
      
      _courseProgress.clear();
      for (String entry in progressData) {
        final parts = entry.split(':');
        if (parts.length == 2) {
          final courseTitle = parts[0];
          final completedLessons = parts[1]
              .split(',')
              .where((s) => s.isNotEmpty)
              .map((s) => int.tryParse(s))
              .where((i) => i != null)
              .cast<int>()
              .toSet();
          _courseProgress[courseTitle] = completedLessons;
        }
      }
    } catch (e) {
      print('Error loading lesson progress: $e');
      _courseProgress = {};
    }
  }

  // Save progress to SharedPreferences
  Future<void> _saveProgress() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final progressData = _courseProgress.entries
          .map((entry) => '${entry.key}:${entry.value.join(',')}')
          .toList();
      await prefs.setStringList(_progressKey, progressData);
    } catch (e) {
      print('Error saving lesson progress: $e');
    }
  }

  // Mark a lesson as completed
  Future<void> markLessonCompleted(String courseTitle, int lessonNumber) async {
    _courseProgress.putIfAbsent(courseTitle, () => <int>{});
    _courseProgress[courseTitle]!.add(lessonNumber);
    await _saveProgress();
  }

  // Mark a lesson as not completed
  Future<void> markLessonNotCompleted(String courseTitle, int lessonNumber) async {
    _courseProgress[courseTitle]?.remove(lessonNumber);
    if (_courseProgress[courseTitle]?.isEmpty == true) {
      _courseProgress.remove(courseTitle);
    }
    await _saveProgress();
  }

  // Check if a lesson is completed
  bool isLessonCompleted(String courseTitle, int lessonNumber) {
    return _courseProgress[courseTitle]?.contains(lessonNumber) ?? false;
  }

  // Get all completed lessons for a course
  Set<int> getCompletedLessons(String courseTitle) {
    return _courseProgress[courseTitle] ?? <int>{};
  }

  // Get progress percentage for a course
  double getCourseProgress(String courseTitle, int totalLessons) {
    final completedCount = getCompletedLessons(courseTitle).length;
    return totalLessons > 0 ? completedCount / totalLessons : 0.0;
  }

  // Get the highest completed lesson number (for "continue watching")
  int getLastCompletedLesson(String courseTitle) {
    final completed = getCompletedLessons(courseTitle);
    return completed.isEmpty ? 0 : completed.reduce((a, b) => a > b ? a : b);
  }

  // Get the next lesson to watch
  int getNextLessonToWatch(String courseTitle, int totalLessons) {
    final lastCompleted = getLastCompletedLesson(courseTitle);
    return lastCompleted < totalLessons ? lastCompleted + 1 : totalLessons;
  }

  // Check if course is fully completed
  bool isCourseCompleted(String courseTitle, int totalLessons) {
    return getCompletedLessons(courseTitle).length == totalLessons;
  }

  // Clear all progress (for testing or reset)
  Future<void> clearAllProgress() async {
    _courseProgress.clear();
    await _saveProgress();
  }

  // Clear progress for a specific course
  Future<void> clearCourseProgress(String courseTitle) async {
    _courseProgress.remove(courseTitle);
    await _saveProgress();
  }
}
