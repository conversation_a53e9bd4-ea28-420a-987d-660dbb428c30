import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../theme/app_theme.dart';
import '../services/lesson_progress_service.dart';
import '../widgets/youtube_player_widget.dart';

class VideoLessonScreen extends StatefulWidget {
  final String lessonTitle;
  final String courseTitle;
  final String instructor;
  final String duration;
  final int currentLesson;
  final int totalLessons;

  const VideoLessonScreen({
    super.key,
    required this.lessonTitle,
    required this.courseTitle,
    required this.instructor,
    required this.duration,
    required this.currentLesson,
    required this.totalLessons,
  });

  @override
  State<VideoLessonScreen> createState() => _VideoLessonScreenState();
}

class _VideoLessonScreenState extends State<VideoLessonScreen>
    with TickerProviderStateMixin {
  bool _isPlaying = false;
  bool _showControls = true;
  bool _isFullscreen = false;
  double _currentPosition = 0.3;
  late AnimationController _controlsAnimationController;
  late AnimationController _playButtonController;
  final LessonProgressService _progressService = LessonProgressService();

  @override
  void initState() {
    super.initState();
    _controlsAnimationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _playButtonController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );

    // Video content ready for device testing

    _startControlsTimer();

    // Mark current lesson as completed when opened
    _markLessonAsCompleted();
  }

  void _markLessonAsCompleted() {
    _progressService.markLessonCompleted(
      widget.courseTitle,
      widget.currentLesson,
    );
  }

  void _startControlsTimer() {
    Future.delayed(const Duration(seconds: 3), () {
      if (mounted && _isPlaying) {
        setState(() {
          _showControls = false;
        });
        _controlsAnimationController.reverse();
      }
    });
  }

  void _togglePlayPause() {
    setState(() {
      _isPlaying = !_isPlaying;
      _showControls = true;
    });

    if (_isPlaying) {
      _playButtonController.forward();
      _startControlsTimer();
    } else {
      _playButtonController.reverse();
    }
    _controlsAnimationController.forward();
  }

  void _toggleFullscreen() {
    debugPrint('Toggling fullscreen: $_isFullscreen -> ${!_isFullscreen}');
    setState(() {
      _isFullscreen = !_isFullscreen;
    });

    if (_isFullscreen) {
      debugPrint('Entering fullscreen mode');
      SystemChrome.setEnabledSystemUIMode(SystemUiMode.immersive);
      SystemChrome.setPreferredOrientations([
        DeviceOrientation.landscapeLeft,
        DeviceOrientation.landscapeRight,
      ]);
    } else {
      debugPrint('Exiting fullscreen mode');
      SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge);
      SystemChrome.setPreferredOrientations([DeviceOrientation.portraitUp]);
    }

    // Show feedback to user
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content:
            Text(_isFullscreen ? 'Entered fullscreen' : 'Exited fullscreen'),
        duration: const Duration(seconds: 1),
        backgroundColor: AppTheme.primaryPink,
      ),
    );
  }

  @override
  void dispose() {
    _controlsAnimationController.dispose();
    _playButtonController.dispose();
    SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge);
    SystemChrome.setPreferredOrientations([DeviceOrientation.portraitUp]);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (_isFullscreen) {
      return _buildFullscreenPlayer();
    }

    return Scaffold(
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      body: SafeArea(
        child: Column(
          children: [
            _buildVideoPlayer(),
            Expanded(
              child: SingleChildScrollView(
                child: Column(
                  children: [
                    _buildLessonInfo(),
                    _buildLessonsList(),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildVideoPlayer() {
    return Container(
      width: double.infinity,
      height: _isFullscreen ? MediaQuery.of(context).size.height : 220,
      color: Colors.black,
      child: Stack(
        children: [
          // Simulator-friendly video placeholder
          _buildVideoContent(),

          // Video controls overlay with navigation
          if (_showControls)
            Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    Colors.black.withOpacity(0.7),
                    Colors.transparent,
                    Colors.black.withOpacity(0.5),
                  ],
                ),
              ),
              child: Stack(
                children: [
                  // Back button (top left)
                  Positioned(
                    top: 16,
                    left: 16,
                    child: Container(
                      decoration: BoxDecoration(
                        color: Colors.black.withOpacity(0.3),
                        shape: BoxShape.circle,
                      ),
                      child: IconButton(
                        onPressed: () => Navigator.pop(context),
                        icon: const Icon(
                          Icons.arrow_back,
                          color: Colors.white,
                          size: 24,
                        ),
                      ),
                    ),
                  ),

                  // Fullscreen button (top right)
                  Positioned(
                    top: 16,
                    right: 16,
                    child: Container(
                      decoration: BoxDecoration(
                        color: Colors.black.withOpacity(0.3),
                        shape: BoxShape.circle,
                      ),
                      child: IconButton(
                        onPressed: _toggleFullscreen,
                        icon: Icon(
                          _isFullscreen
                              ? Icons.fullscreen_exit
                              : Icons.fullscreen,
                          color: Colors.white,
                          size: 24,
                        ),
                      ),
                    ),
                  ),

                  // Play/Pause button (center)
                  Center(
                    child: GestureDetector(
                      onTap: _togglePlayPause,
                      child: Container(
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: Colors.white.withOpacity(0.2),
                          shape: BoxShape.circle,
                        ),
                        child: Icon(
                          _isPlaying ? Icons.pause : Icons.play_arrow,
                          color: Colors.white,
                          size: 40,
                        ),
                      ),
                    ),
                  ),

                  // Progress bar (bottom)
                  Positioned(
                    bottom: 16,
                    left: 16,
                    right: 16,
                    child: Row(
                      children: [
                        Text(
                          '${(_currentPosition * 100).toInt()}:30',
                          style: const TextStyle(
                              color: Colors.white, fontSize: 12),
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: SliderTheme(
                            data: SliderTheme.of(context).copyWith(
                              activeTrackColor: AppTheme.primaryPink,
                              inactiveTrackColor: Colors.white.withOpacity(0.3),
                              thumbColor: AppTheme.primaryPink,
                              overlayColor:
                                  AppTheme.primaryPink.withOpacity(0.2),
                              trackHeight: 2,
                              thumbShape: const RoundSliderThumbShape(
                                  enabledThumbRadius: 5),
                            ),
                            child: Slider(
                              value: _currentPosition,
                              onChanged: (value) {
                                setState(() {
                                  _currentPosition = value;
                                });
                              },
                            ),
                          ),
                        ),
                        const SizedBox(width: 8),
                        Text(
                          widget.duration,
                          style: const TextStyle(
                              color: Colors.white, fontSize: 12),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),

          // Tap to show/hide controls
          GestureDetector(
            onTap: () {
              setState(() {
                _showControls = !_showControls;
              });
              if (_showControls) {
                _controlsAnimationController.forward();
                _startControlsTimer();
              } else {
                _controlsAnimationController.reverse();
              }
            },
            child: Container(
              width: double.infinity,
              height: double.infinity,
              color: Colors.transparent,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildVideoContent() {
    // Real YouTube player for device
    return YouTubePlayerWidget(
      videoId: _getVideoIdForLesson(widget.currentLesson),
      onReady: () {
        debugPrint('YouTube player ready for lesson ${widget.currentLesson}');
      },
      onVideoEnd: () {
        // Auto-mark lesson as completed when video ends
        _markLessonAsCompleted();
        // Navigate to next lesson if available
        if (widget.currentLesson < widget.totalLessons) {
          _navigateToLesson(widget.currentLesson + 1);
        }
      },
    );
  }

  Widget _buildFullscreenPlayer() {
    return Scaffold(
      backgroundColor: Colors.black,
      body: _buildVideoPlayer(),
    );
  }

  Widget _buildLessonInfo() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Lesson title
          Text(
            widget.lessonTitle,
            style: Theme.of(context).textTheme.headlineSmall,
          ),
          const SizedBox(height: 8),

          // Course info with navigation
          Row(
            children: [
              Flexible(
                child: GestureDetector(
                  onTap: () {
                    Navigator.popUntil(context, (route) => route.isFirst);
                  },
                  child: Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: AppTheme.primaryPink.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(6),
                      border: Border.all(
                        color: AppTheme.primaryPink.withOpacity(0.3),
                        width: 1,
                      ),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        const Icon(
                          Icons.arrow_back,
                          size: 14,
                          color: AppTheme.primaryPink,
                        ),
                        const SizedBox(width: 4),
                        Flexible(
                          child: Text(
                            widget.courseTitle,
                            style:
                                Theme.of(context).textTheme.bodySmall?.copyWith(
                                      color: AppTheme.primaryPink,
                                      fontWeight: FontWeight.w600,
                                    ),
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  'by ${widget.instructor}',
                  style: Theme.of(context).textTheme.bodyMedium,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),

          // Progress indicator
          Row(
            children: [
              Text(
                'Lesson ${widget.currentLesson} of ${widget.totalLessons}',
                style: Theme.of(context).textTheme.bodyMedium,
              ),
              const SizedBox(width: 12),
              Expanded(
                child: LinearProgressIndicator(
                  value: _progressService.getCourseProgress(
                    widget.courseTitle,
                    widget.totalLessons,
                  ),
                  backgroundColor: AppTheme.primaryPink.withOpacity(0.2),
                  valueColor:
                      const AlwaysStoppedAnimation<Color>(AppTheme.primaryPink),
                ),
              ),
              const SizedBox(width: 12),
              Text(
                '${(_progressService.getCourseProgress(widget.courseTitle, widget.totalLessons) * 100).toInt()}%',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: AppTheme.primaryPink,
                      fontWeight: FontWeight.w600,
                    ),
              ),
            ],
          ),
          const SizedBox(height: 16),

          // Navigation and action buttons
          Row(
            children: [
              // Previous lesson button
              if (widget.currentLesson > 1)
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: () {
                      Navigator.pushReplacement(
                        context,
                        MaterialPageRoute(
                          builder: (context) => VideoLessonScreen(
                            lessonTitle:
                                'Lesson ${widget.currentLesson - 1}: ${_getLessonTitle(widget.currentLesson - 1)}',
                            courseTitle: widget.courseTitle,
                            instructor: widget.instructor,
                            duration:
                                _getLessonDuration(widget.currentLesson - 1),
                            currentLesson: widget.currentLesson - 1,
                            totalLessons: widget.totalLessons,
                          ),
                        ),
                      );
                    },
                    icon: const Icon(Icons.skip_previous, size: 18),
                    label: const Text('Previous'),
                  ),
                ),

              if (widget.currentLesson > 1) const SizedBox(width: 8),

              // Save button
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: () {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(content: Text('Added to favorites!')),
                    );
                  },
                  icon: const Icon(Icons.favorite_border, size: 18),
                  label: const Text('Save'),
                ),
              ),

              const SizedBox(width: 8),

              // Next lesson button
              if (widget.currentLesson < widget.totalLessons)
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: () {
                      Navigator.pushReplacement(
                        context,
                        MaterialPageRoute(
                          builder: (context) => VideoLessonScreen(
                            lessonTitle:
                                'Lesson ${widget.currentLesson + 1}: ${_getLessonTitle(widget.currentLesson + 1)}',
                            courseTitle: widget.courseTitle,
                            instructor: widget.instructor,
                            duration:
                                _getLessonDuration(widget.currentLesson + 1),
                            currentLesson: widget.currentLesson + 1,
                            totalLessons: widget.totalLessons,
                          ),
                        ),
                      );
                    },
                    icon: const Icon(Icons.skip_next, size: 18),
                    label: const Text('Next'),
                  ),
                ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildLessonsList() {
    return Container(
      height: 400, // Fixed height to prevent overflow
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(24),
          topRight: Radius.circular(24),
        ),
      ),
      child: Column(
        children: [
          // Section header
          Container(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                Text(
                  'Course Content',
                  style: Theme.of(context).textTheme.headlineSmall,
                ),
                const Spacer(),
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: AppTheme.accentTeal.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(6),
                  ),
                  child: Text(
                    '${widget.totalLessons} lessons',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: AppTheme.accentTeal,
                          fontWeight: FontWeight.w600,
                        ),
                  ),
                ),
              ],
            ),
          ),

          // Lessons list
          Expanded(
            child: ListView.builder(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              itemCount: widget.totalLessons,
              itemBuilder: (context, index) {
                final lessonNumber = index + 1;
                final isCurrentLesson = lessonNumber == widget.currentLesson;
                final isCompleted = _progressService.isLessonCompleted(
                  widget.courseTitle,
                  lessonNumber,
                );

                return Container(
                  margin: const EdgeInsets.only(bottom: 8),
                  decoration: BoxDecoration(
                    color: isCurrentLesson
                        ? AppTheme.primaryPink.withOpacity(0.1)
                        : Colors.transparent,
                    borderRadius: BorderRadius.circular(12),
                    border: isCurrentLesson
                        ? Border.all(
                            color: AppTheme.primaryPink.withOpacity(0.3))
                        : null,
                  ),
                  child: ListTile(
                    leading: Container(
                      width: 40,
                      height: 40,
                      decoration: BoxDecoration(
                        color: isCompleted
                            ? AppTheme.accentTeal
                            : isCurrentLesson
                                ? AppTheme.primaryPink
                                : AppTheme.textSecondary.withOpacity(0.2),
                        shape: BoxShape.circle,
                      ),
                      child: Icon(
                        isCompleted ? Icons.check : Icons.play_arrow,
                        color: isCompleted || isCurrentLesson
                            ? Colors.white
                            : AppTheme.textSecondary,
                        size: 20,
                      ),
                    ),
                    title: Text(
                      'Lesson $lessonNumber: ${_getLessonTitle(lessonNumber)}',
                      style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                            fontWeight: isCurrentLesson
                                ? FontWeight.w600
                                : FontWeight.normal,
                            color:
                                isCurrentLesson ? AppTheme.primaryPink : null,
                          ),
                    ),
                    subtitle: Text(
                      '${_getLessonDuration(lessonNumber)} • ${isCompleted ? 'Completed' : isCurrentLesson ? 'Current' : 'Not started'}',
                      style: Theme.of(context).textTheme.bodySmall,
                    ),
                    trailing: isCompleted
                        ? const Icon(Icons.check_circle,
                            color: AppTheme.accentTeal, size: 20)
                        : null,
                    onTap: () {
                      // Navigate to the selected lesson
                      _navigateToLesson(lessonNumber);
                    },
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  String _getLessonTitle(int lessonNumber) {
    final titles = [
      'Introduction to Flutter',
      'Setting up Development Environment',
      'Understanding Widgets',
      'Building Your First App',
      'State Management Basics',
      'Navigation and Routing',
      'Working with APIs',
      'Local Storage Solutions',
      'Testing Your App',
      'Publishing to App Stores',
    ];
    return titles[(lessonNumber - 1) % titles.length];
  }

  String _getLessonDuration(int lessonNumber) {
    final durations = [
      '12:30',
      '8:45',
      '15:20',
      '22:10',
      '18:35',
      '14:25',
      '20:15',
      '16:40',
      '11:55',
      '25:30'
    ];
    return durations[(lessonNumber - 1) % durations.length];
  }

  // Get YouTube video ID for each lesson (using free educational videos)
  String _getVideoIdForLesson(int lessonNumber) {
    const videoIds = [
      'VPRjCeoBqrI', // Flutter Tutorial for Beginners #1 - Introduction
      'GLSG_Wh_YWc', // Flutter Tutorial for Beginners #2 - Setup
      'wE7khGHVkYY', // Flutter Tutorial for Beginners #3 - Widgets
      'I9ceqw5Ny-4', // Flutter Tutorial for Beginners #4 - First App
      'WwhyaqNtNQY', // Flutter Tutorial for Beginners #5 - State Management
      'YdUQULuJXKY', // Flutter Tutorial for Beginners #6 - Navigation
      'G7yjLWCYXl4', // Flutter Tutorial for Beginners #7 - APIs
      'HQ_ytw58tC4', // Flutter Tutorial for Beginners #8 - Storage
      'iflV0D0d1zQ', // Flutter Tutorial for Beginners #9 - Testing
      'ciog4dGAeY4', // Flutter Tutorial for Beginners #10 - Publishing
    ];
    return videoIds[(lessonNumber - 1) % videoIds.length];
  }

  // Helper method to navigate to a specific lesson
  void _navigateToLesson(int lessonNumber) {
    Navigator.pushReplacement(
      context,
      MaterialPageRoute(
        builder: (context) => VideoLessonScreen(
          lessonTitle: 'Lesson $lessonNumber: ${_getLessonTitle(lessonNumber)}',
          courseTitle: widget.courseTitle,
          instructor: widget.instructor,
          duration: _getLessonDuration(lessonNumber),
          currentLesson: lessonNumber,
          totalLessons: widget.totalLessons,
        ),
      ),
    );
  }
}
