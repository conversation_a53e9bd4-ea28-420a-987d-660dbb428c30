import 'package:flutter/material.dart';
import 'package:youtube_player_flutter/youtube_player_flutter.dart';
import '../theme/app_theme.dart';

class YouTubePlayerWidget extends StatefulWidget {
  final String videoId;
  final VoidCallback? onVideoEnd;
  final VoidCallback? onReady;

  const YouTubePlayerWidget({
    super.key,
    required this.videoId,
    this.onVideoEnd,
    this.onReady,
  });

  @override
  State<YouTubePlayerWidget> createState() => _YouTubePlayerWidgetState();
}

class _YouTubePlayerWidgetState extends State<YouTubePlayerWidget> {
  late YoutubePlayerController _controller;
  bool _isPlayerReady = false;

  @override
  void initState() {
    super.initState();
    _controller = YoutubePlayerController(
      initialVideoId: widget.videoId,
      flags: const YoutubePlayerFlags(
        autoPlay: true,
        mute: false,
        enableCaption: true,
        captionLanguage: 'en',
        forceHD: false,
        loop: false,
        controlsVisibleAtStart: true,
        hideControls: false,
        disableDragSeek: false,
        useHybridComposition: true,
      ),
    );

    _controller.addListener(_listener);
  }

  void _listener() {
    if (_isPlayerReady && mounted) {
      if (_controller.value.playerState == PlayerState.ended) {
        widget.onVideoEnd?.call();
      }
      // Trigger rebuild to update play/pause button
      setState(() {});
    }
  }

  @override
  void dispose() {
    _controller.removeListener(_listener);
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return YoutubePlayerBuilder(
      onExitFullScreen: () {
        // Handle exit fullscreen
      },
      player: YoutubePlayer(
        controller: _controller,
        showVideoProgressIndicator: false,
        progressIndicatorColor: AppTheme.mangaWhite,
        progressColors: ProgressBarColors(
          playedColor: AppTheme.mangaWhite,
          handleColor: AppTheme.mangaWhite,
        ),
        onReady: () {
          _isPlayerReady = true;
          widget.onReady?.call();
        },
        onEnded: (data) {
          widget.onVideoEnd?.call();
        },
        bottomActions: [
          CurrentPosition(),
          const SizedBox(width: 10.0),
          ProgressBar(isExpanded: true),
          const SizedBox(width: 10.0),
          RemainingDuration(),
          FullScreenButton(),
        ],
      ),
      builder: (context, player) {
        return Column(
          children: [
            player,
            Container(
              padding: const EdgeInsets.all(8.0),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  IconButton(
                    onPressed: () {
                      _controller.seekTo(
                        Duration(
                          seconds: _controller.value.position.inSeconds - 10,
                        ),
                      );
                    },
                    icon: const Icon(Icons.replay_10),
                    tooltip: 'Rewind 10s',
                  ),
                  IconButton(
                    onPressed: () {
                      if (_controller.value.isPlaying) {
                        _controller.pause();
                      } else {
                        _controller.play();
                      }
                    },
                    icon: Icon(
                      _controller.value.isPlaying
                          ? Icons.pause
                          : Icons.play_arrow,
                    ),
                    tooltip: 'Play/Pause',
                  ),
                  IconButton(
                    onPressed: () {
                      _controller.seekTo(
                        Duration(
                          seconds: _controller.value.position.inSeconds + 10,
                        ),
                      );
                    },
                    icon: const Icon(Icons.forward_10),
                    tooltip: 'Forward 10s',
                  ),
                  IconButton(
                    onPressed: () {
                      widget.onVideoEnd?.call();
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                          content: Text('Lesson marked as completed!'),
                          backgroundColor: AppTheme.mangaRed,
                          duration: Duration(seconds: 2),
                        ),
                      );
                    },
                    icon: const Icon(Icons.check_circle),
                    tooltip: 'Mark Complete',
                    color: AppTheme.mangaRed,
                  ),
                ],
              ),
            ),
          ],
        );
      },
    );
  }
}
