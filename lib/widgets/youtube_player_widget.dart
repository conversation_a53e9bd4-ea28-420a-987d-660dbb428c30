import 'package:flutter/material.dart';
import 'package:youtube_player_flutter/youtube_player_flutter.dart';
import '../theme/app_theme.dart';

class YouTubePlayerWidget extends StatefulWidget {
  final String videoId;
  final VoidCallback? onVideoEnd;
  final VoidCallback? onReady;

  const YouTubePlayerWidget({
    super.key,
    required this.videoId,
    this.onVideoEnd,
    this.onReady,
  });

  @override
  State<YouTubePlayerWidget> createState() => _YouTubePlayerWidgetState();
}

class _YouTubePlayerWidgetState extends State<YouTubePlayerWidget> {
  late YoutubePlayerController _controller;
  bool _isPlayerReady = false;

  @override
  void initState() {
    super.initState();
    _controller = YoutubePlayerController(
      initialVideoId: widget.videoId,
      flags: const YoutubePlayerFlags(
        autoPlay: false,
        mute: false,
        enableCaption: true,
        captionLanguage: 'en',
        forceHD: false,
        loop: false,
      ),
    );

    _controller.addListener(_listener);
  }

  void _listener() {
    if (_isPlayerReady && mounted) {
      if (_controller.value.playerState == PlayerState.ended) {
        widget.onVideoEnd?.call();
      }
    }
  }

  @override
  void dispose() {
    _controller.removeListener(_listener);
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return YoutubePlayerBuilder(
      onExitFullScreen: () {
        // Handle exit fullscreen
      },
      player: YoutubePlayer(
        controller: _controller,
        showVideoProgressIndicator: true,
        progressIndicatorColor: AppTheme.primaryPink,
        onReady: () {
          _isPlayerReady = true;
          widget.onReady?.call();
        },
        onEnded: (data) {
          widget.onVideoEnd?.call();
        },
      ),
      builder: (context, player) {
        return Column(
          children: [
            player,
            // Optional: Add custom controls or information here
          ],
        );
      },
    );
  }
}
