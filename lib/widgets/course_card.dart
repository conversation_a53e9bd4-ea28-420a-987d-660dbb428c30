import 'package:flutter/material.dart';
import '../screens/video_lesson_screen.dart';
import '../services/lesson_progress_service.dart';

class CourseCard extends StatelessWidget {
  final String title;
  final String instructor;
  final String duration;
  final double rating;
  final String imageUrl;
  final VoidCallback? onTap;
  final Color? accentColor;

  const CourseCard({
    super.key,
    required this.title,
    required this.instructor,
    required this.duration,
    required this.rating,
    required this.imageUrl,
    this.onTap,
    this.accentColor,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final cardAccentColor = accentColor ?? theme.colorScheme.primary;
    final progressService = LessonProgressService();
    final totalLessons = 10; // Default total lessons
    final courseProgress =
        progressService.getCourseProgress(title, totalLessons);
    final nextLesson =
        progressService.getNextLessonToWatch(title, totalLessons);
    final isCompleted = progressService.isCourseCompleted(title, totalLessons);

    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(16),
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16),
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                cardAccentColor.withOpacity(0.1),
                cardAccentColor.withOpacity(0.05),
              ],
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Course Image with overlay
              Container(
                height: 120,
                width: double.infinity,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(12),
                  color: cardAccentColor.withOpacity(0.2),
                ),
                child: Stack(
                  children: [
                    // Placeholder for course image
                    Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(12),
                        gradient: LinearGradient(
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                          colors: [
                            cardAccentColor.withOpacity(0.8),
                            cardAccentColor.withOpacity(0.6),
                          ],
                        ),
                      ),
                      child: Center(
                        child: Icon(
                          Icons.play_circle_filled,
                          size: 48,
                          color: Colors.white.withOpacity(0.9),
                        ),
                      ),
                    ),
                    // Duration badge
                    Positioned(
                      top: 8,
                      right: 8,
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 4,
                        ),
                        decoration: BoxDecoration(
                          color: Colors.black.withOpacity(0.7),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Text(
                          duration,
                          style: theme.textTheme.bodySmall?.copyWith(
                            color: Colors.white,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 12),

              // Course Title
              Text(
                title,
                style: theme.textTheme.headlineSmall?.copyWith(
                  fontSize: 18,
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
              const SizedBox(height: 4),

              // Instructor
              Text(
                'by $instructor',
                style: theme.textTheme.bodyMedium,
              ),
              const SizedBox(height: 8),

              // Progress bar (if course has been started)
              if (courseProgress > 0) ...[
                const SizedBox(height: 8),
                Row(
                  children: [
                    Text(
                      'Progress: ${(courseProgress * 100).toInt()}%',
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: cardAccentColor,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: LinearProgressIndicator(
                        value: courseProgress,
                        backgroundColor: cardAccentColor.withOpacity(0.2),
                        valueColor: AlwaysStoppedAnimation<Color>(
                          isCompleted ? Colors.green : cardAccentColor,
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
              ],

              // Rating and additional info
              Row(
                children: [
                  // Rating
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: cardAccentColor.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(6),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          Icons.star,
                          size: 14,
                          color: cardAccentColor,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          rating.toString(),
                          style: theme.textTheme.bodySmall?.copyWith(
                            color: cardAccentColor,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ],
                    ),
                  ),
                  const Spacer(),

                  // Action button with progress-aware text
                  GestureDetector(
                    onTap: () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => VideoLessonScreen(
                            lessonTitle: nextLesson == 1
                                ? 'Introduction to $title'
                                : 'Lesson $nextLesson: ${_getLessonTitle(nextLesson)}',
                            courseTitle: title,
                            instructor: instructor,
                            duration: duration,
                            currentLesson: nextLesson,
                            totalLessons: totalLessons,
                          ),
                        ),
                      );
                    },
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 12,
                        vertical: 6,
                      ),
                      decoration: BoxDecoration(
                        color: isCompleted
                            ? Colors.green
                            : courseProgress > 0
                                ? Colors.orange
                                : cardAccentColor,
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Text(
                        isCompleted
                            ? 'Completed'
                            : courseProgress > 0
                                ? 'Continue'
                                : 'Start',
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: Colors.white,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  String _getLessonTitle(int lessonNumber) {
    const titles = [
      'Introduction to Flutter',
      'Setting up Development Environment',
      'Understanding Widgets',
      'Building Your First App',
      'State Management Basics',
      'Navigation and Routing',
      'Working with APIs',
      'Local Storage Solutions',
      'Testing Your App',
      'Publishing to App Stores',
    ];
    return titles[(lessonNumber - 1) % titles.length];
  }
}
