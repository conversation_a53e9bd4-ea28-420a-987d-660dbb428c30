import 'package:flutter/material.dart';
import 'dart:math' as math;

class ModernBackground extends StatelessWidget {
  final Widget child;

  const ModernBackground({super.key, required this.child});

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        // Gradient
        Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                Theme.of(context).colorScheme.surface,
                Theme.of(context).colorScheme.secondary,
              ],
            ),
          ),
        ),
        // Shape 1 (top right circle)
        Positioned(
          top: -100,
          right: -150,
          child: Container(
            width: 300,
            height: 300,
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.primary,
              shape: BoxShape.circle,
            ),
          ),
        ),
        // Shape 2 (bottom left circle)
        Positioned(
          bottom: -50,
          left: -100,
          child: Container(
            width: 200,
            height: 200,
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.secondary,
              shape: BoxShape.circle,
            ),
          ),
        ),
        // New "line" shape
        Positioned(
          bottom: MediaQuery.of(context).size.height * 0.2,
          right: -150,
          child: Transform.rotate(
            angle: -math.pi / 4, // -45 degrees
            child: Container(
              width: 400,
              height: 2,
              color: Theme.of(context).colorScheme.onSurface,
            ),
          ),
        ),
        // The actual content of the page
        child,
      ],
    );
  }
}
