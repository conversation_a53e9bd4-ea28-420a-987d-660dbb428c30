import 'package:flutter/material.dart';

class AppTheme {
  // Manga-inspired colors
  static const Color primaryBlack = Color(0xFF121212);
  static const Color mangaWhite = Color(0xFFF5F5F5);
  static const Color inkBlack = Color(0xFF000000);
  static const Color mangaRed = Color(0xFFE53935);
  static const Color mangaBlue = Color(0xFF1E88E5);
  static const Color screenTone = Color(0xFFE0E0E0);

  // Get light theme data
  static ThemeData get lightTheme {
    return ThemeData(
      useMaterial3: true,
      colorScheme: const ColorScheme.light(
        primary: mangaRed,
        secondary: mangaRed,
        surface: mangaWhite,
        onPrimary: mangaWhite,
        onSecondary: mangaWhite,
        onSurface: inkBlack,
        error: mangaRed,
      ),
      scaffoldBackgroundColor: mangaWhite,
      appBarTheme: const AppBarTheme(
        backgroundColor: inkBlack,
        foregroundColor: mangaWhite,
        elevation: 0,
      ),
      textTheme: _buildMangaTextTheme(Brightness.light),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: mangaRed,
          foregroundColor: mangaWhite,
          elevation: 2,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
      ),
      outlinedButtonTheme: OutlinedButtonThemeData(
        style: OutlinedButton.styleFrom(
          foregroundColor: inkBlack,
          side: const BorderSide(color: inkBlack, width: 1.5),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
      ),
      cardTheme: CardThemeData(
        color: mangaWhite,
        elevation: 2,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
          side: const BorderSide(color: screenTone, width: 1),
        ),
      ),
      dividerTheme: const DividerThemeData(
        color: screenTone,
        thickness: 1,
      ),
    );
  }

  // Get dark theme data
  static ThemeData get darkTheme {
    return ThemeData(
      useMaterial3: true,
      colorScheme: const ColorScheme.dark(
        primary: mangaWhite,
        secondary: mangaRed,
        surface: primaryBlack,
        onPrimary: inkBlack,
        onSecondary: mangaWhite,
        onSurface: mangaWhite,
        error: mangaRed,
      ),
      scaffoldBackgroundColor: primaryBlack,
      appBarTheme: const AppBarTheme(
        backgroundColor: inkBlack,
        foregroundColor: mangaWhite,
        elevation: 0,
      ),
      textTheme: _buildMangaTextTheme(Brightness.dark),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: mangaRed,
          foregroundColor: mangaWhite,
          elevation: 2,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
      ),
      outlinedButtonTheme: OutlinedButtonThemeData(
        style: OutlinedButton.styleFrom(
          foregroundColor: mangaWhite,
          side: const BorderSide(color: mangaWhite, width: 1.5),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
      ),
      cardTheme: CardThemeData(
        color: primaryBlack,
        elevation: 2,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
          side: const BorderSide(color: Color(0xFF333333), width: 1),
        ),
      ),
      dividerTheme: const DividerThemeData(
        color: Color(0xFF333333),
        thickness: 1,
      ),
    );
  }

  // Build manga-style text theme
  static TextTheme _buildMangaTextTheme(Brightness brightness) {
    final textColor = brightness == Brightness.light ? inkBlack : mangaWhite;

    return TextTheme(
      displayLarge: TextStyle(
        fontFamily: 'MangaFont',
        color: textColor,
        fontWeight: FontWeight.bold,
      ),
      headlineMedium: TextStyle(
        fontFamily: 'MangaFont',
        color: textColor,
        fontWeight: FontWeight.bold,
      ),
      bodyLarge: TextStyle(
        fontFamily: 'MangaFont',
        color: textColor,
      ),
      bodyMedium: TextStyle(
        fontFamily: 'MangaFont',
        color: textColor,
      ),
      labelLarge: TextStyle(
        fontFamily: 'MangaFont',
        color: textColor,
        fontWeight: FontWeight.bold,
      ),
    );
  }
}
