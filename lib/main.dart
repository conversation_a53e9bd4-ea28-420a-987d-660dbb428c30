import 'package:flutter/material.dart';
import 'first_page.dart';
import 'home_page.dart';
import 'theme/app_theme.dart';
import 'services/lesson_progress_service.dart';
import 'user_manager.dart';

void main() async {
  // Ensure Flutter binding is initialized before using plugins.
  WidgetsFlutterBinding.ensureInitialized();
  // Asynchronously initialize the UserManager to ensure it's ready before use.
  await UserManager.init();
  final userManager = UserManager();

  // Initialize lesson progress service
  await LessonProgressService().initialize();

  // Load initial user and theme settings from persistence.
  final initialSavedUser = await userManager.getSavedUser();
  final initialThemeMode = await userManager.getThemeMode();

  runApp(MyApp(
    initialSavedUser: initialSavedUser,
    userManager: userManager,
    initialThemeMode: initialThemeMode,
  ));
}

class MyApp extends StatefulWidget {
  final String? initialSavedUser;
  final UserManager
      userManager; // Pass UserManager to MyApp for state management
  final ThemeMode initialThemeMode;

  const MyApp(
      {super.key,
      this.initialSavedUser,
      required this.userManager,
      required this.initialThemeMode});

  @override
  State<MyApp> createState() => MyAppState();
}

class MyAppState extends State<MyApp> {
  late ThemeMode _themeMode;
  String? _currentLoggedInUser; // Internal state to manage the logged-in user.

  @override
  void initState() {
    super.initState();
    _currentLoggedInUser = widget.initialSavedUser;
    _themeMode = widget.initialThemeMode;
  }

  void setThemeMode(ThemeMode mode) {
    setState(() {
      _themeMode = mode;
    });
    widget.userManager.saveThemeMode(mode); // Persist the theme change.
  }

  // Callback to update the logged-in user state after successful login.
  void _handleLoginSuccess(String username) {
    // Assuming LoginPage calls userManager.saveUser(username) internally.
    // If not, it should be called here: await widget.userManager.saveUser(username);
    setState(() {
      _currentLoggedInUser = username;
    });
  }

  // Callback to clear the logged-in user state after logout.
  void _handleLogout() async {
    await widget.userManager.logoutUser(); // Clear user from persistence.
    setState(() {
      _currentLoggedInUser = null;
    });
  }

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Flutter Demo',
      debugShowCheckedModeBanner: false,
      theme: AppTheme.lightTheme,
      darkTheme: AppTheme.darkTheme,
      themeMode: _themeMode,
      home: _currentLoggedInUser != null
          ? HomePage(username: _currentLoggedInUser!, onLogout: _handleLogout)
          : LoginPage(
              setThemeMode: setThemeMode, onLoginSuccess: _handleLoginSuccess),
      routes: {
        '/first_page': (context) => LoginPage(
            setThemeMode: setThemeMode, onLoginSuccess: _handleLoginSuccess),
        '/home': (context) => _currentLoggedInUser != null
            ? HomePage(username: _currentLoggedInUser!, onLogout: _handleLogout)
            : LoginPage(
                setThemeMode: setThemeMode,
                onLoginSuccess: _handleLoginSuccess),
      },
    );
  }
}
